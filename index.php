<?php
session_start();

// Include the JWT Lab class
require_once 'jwt-lab.php';

$jwt = new JWTLab();
$currentUser = $jwt->getCurrentUser();
$error = '';
$success = '';

// Redirect to dashboard if already logged in
if ($currentUser) {
    header('Location: dashboard.php');
    exit;
}

// Handle login
if ($_POST['action'] ?? '' === 'login') {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';

    $user = $jwt->authenticate($username, $password);

    if ($user) {
        $payload = [
            'iss' => 'jwt-weak-secret-lab',
            'aud' => 'lab-users',
            'iat' => time(),
            'exp' => time() + (60 * 60),
            'sub' => $user['id'],
            'username' => $user['username'],
            'role' => $user['role']
        ];

        $token = $jwt->createToken($payload);
        setcookie('jwt_token', $token, time() + 3600, '/');
        header('Location: dashboard.php');
        exit;
    } else {
        $error = 'Invalid credentials';
    }
}

// Handle logout
if ($_GET['action'] ?? '' === 'logout') {
    setcookie('jwt_token', '', time() - 3600, '/');
    header('Location: index.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JWT Weak Secret Lab - Login</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-shield-alt"></i>
                    <h1>JWT Weak Secret Lab</h1>
                </div>
                <div class="app-title">
                    <h2>Login Portal</h2>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Login Form -->
            <section class="login-section">
                <div class="login-container">
                    <div class="login-header">
                        <i class="fas fa-lock"></i>
                        <h2>Login to JWT Lab</h2>
                        <p>Enter your credentials to access the system</p>
                    </div>

                    <?php if ($error): ?>
                        <div class="message-box error">
                            <?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>

                    <form method="POST" class="login-form">
                        <input type="hidden" name="action" value="login">

                        <div class="form-group">
                            <label for="username">
                                <i class="fas fa-user"></i>
                                Username
                            </label>
                            <input type="text" id="username" name="username" class="form-input" placeholder="Enter username" required>
                        </div>

                        <div class="form-group">
                            <label for="password">
                                <i class="fas fa-key"></i>
                                Password
                            </label>
                            <input type="password" id="password" name="password" class="form-input" placeholder="Enter password" required>
                        </div>

                        <button type="submit" class="btn btn-login">
                            <i class="fas fa-sign-in-alt"></i>
                            Login
                        </button>
                    </form>

                    <div class="login-hint">
                        <i class="fas fa-info-circle"></i>
                        Try logging in with <code>guest:guest</code>
                    </div>
                </div>
            </section>

            <!-- Challenge Description -->
            <section class="challenge-section">
                <div class="challenge-card">
                    <div class="challenge-header">
                        <i class="fas fa-flag"></i>
                        <h3>Challenge: JWT Weak Secret Attack</h3>
                    </div>
                    <div class="challenge-content">
                        <p>Your mission is to exploit a JWT weak secret vulnerability to gain admin access and capture the flag.</p>

                        <div class="challenge-objectives">
                            <h4>Objective:</h4>
                            <ul>
                                <li>Login as guest:guest to get a JWT token</li>
                                <li>Analyze the JWT token structure</li>
                                <li>Brute-force or guess the weak signing secret</li>
                                <li>Forge a new JWT token with admin privileges</li>
                                <li>Access the admin dashboard and capture the flag!</li>
                            </ul>
                        </div>

                        <div class="challenge-hint">
                            <i class="fas fa-lightbulb"></i>
                            <p><strong>Hint:</strong> The JWT secret might be something very simple like "123", "secret", "admin", or "password". Try common weak passwords!</p>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <p>&copy; 2024 JWT Weak Secret Lab</p>
            </div>
        </div>
    </footer>
</body>
</html>
