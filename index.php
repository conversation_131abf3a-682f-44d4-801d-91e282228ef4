<?php
session_start();

// JWT None Algorithm Attack Lab - Pure PHP
class JWTLab {
    private $secret = 'super_secret_key_123';
    private $users = [
        'guest' => [
            'password' => 'guest',
            'role' => 'guest',
            'id' => 1
        ],
        'admin' => [
            'password' => 'super_secret_admin_password_2024',
            'role' => 'admin',
            'id' => 2
        ]
    ];
    private $flag = 'CB{JWT_N0N3_4LG0R1THM_PWN3D}';

    // Base64 URL encode
    private function base64UrlEncode($data) {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }

    // Base64 URL decode
    private function base64UrlDecode($data) {
        return base64_decode(str_pad(strtr($data, '-_', '+/'), strlen($data) % 4, '=', STR_PAD_RIGHT));
    }

    // Create JWT token
    public function createToken($payload) {
        $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
        $payload = json_encode($payload);
        
        $headerEncoded = $this->base64UrlEncode($header);
        $payloadEncoded = $this->base64UrlEncode($payload);
        
        $signature = hash_hmac('sha256', $headerEncoded . '.' . $payloadEncoded, $this->secret, true);
        $signatureEncoded = $this->base64UrlEncode($signature);
        
        return $headerEncoded . '.' . $payloadEncoded . '.' . $signatureEncoded;
    }

    // VULNERABLE: Verify JWT token - accepts "none" algorithm
    public function verifyToken($token) {
        $parts = explode('.', $token);
        
        if (count($parts) !== 3) {
            return false;
        }

        $header = json_decode($this->base64UrlDecode($parts[0]), true);
        $payload = json_decode($this->base64UrlDecode($parts[1]), true);
        
        if (!$header || !$payload) {
            return false;
        }

        // VULNERABILITY: Accept "none" algorithm without signature verification
        if ($header['alg'] === 'none') {
            return $payload;
        }

        // For other algorithms, verify signature
        if ($header['alg'] === 'HS256') {
            $headerEncoded = $parts[0];
            $payloadEncoded = $parts[1];
            $signature = $parts[2];
            
            $expectedSignature = $this->base64UrlEncode(
                hash_hmac('sha256', $headerEncoded . '.' . $payloadEncoded, $this->secret, true)
            );
            
            if ($signature === $expectedSignature) {
                return $payload;
            }
        }

        return false;
    }

    // Authenticate user
    public function authenticate($username, $password) {
        if (isset($this->users[$username]) && $this->users[$username]['password'] === $password) {
            return [
                'id' => $this->users[$username]['id'],
                'username' => $username,
                'role' => $this->users[$username]['role']
            ];
        }
        return false;
    }

    // Get current user from token
    public function getCurrentUser() {
        if (isset($_COOKIE['jwt_token'])) {
            $payload = $this->verifyToken($_COOKIE['jwt_token']);
            if ($payload) {
                return $payload;
            }
        }
        return null;
    }

    // Get flag (only for admin)
    public function getFlag() {
        return $this->flag;
    }
}

$jwt = new JWTLab();
$currentUser = $jwt->getCurrentUser();
$error = '';
$success = '';

// Handle login
if ($_POST['action'] ?? '' === 'login') {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    $user = $jwt->authenticate($username, $password);
    
    if ($user) {
        $payload = [
            'iss' => 'cyber-bangla-lab',
            'aud' => 'secureapp-users',
            'iat' => time(),
            'exp' => time() + (60 * 60),
            'sub' => $user['id'],
            'username' => $user['username'],
            'role' => $user['role']
        ];
        
        $token = $jwt->createToken($payload);
        setcookie('jwt_token', $token, time() + 3600, '/');
        header('Location: index.php');
        exit;
    } else {
        $error = 'Invalid credentials';
    }
}

// Handle logout
if ($_GET['action'] ?? '' === 'logout') {
    setcookie('jwt_token', '', time() - 3600, '/');
    header('Location: index.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $currentUser ? 'SecureApp Dashboard' : 'SecureApp Login'; ?> - Cyber Bangla Lab</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-shield-alt"></i>
                    <h1>Cyber Bangla Lab</h1>
                </div>
                <?php if ($currentUser): ?>
                <div class="user-info">
                    <span>Welcome, <strong><?php echo htmlspecialchars($currentUser['username']); ?></strong></span>
                    <span class="user-role"><?php echo htmlspecialchars($currentUser['role']); ?></span>
                    <a href="?action=logout" class="btn btn-logout">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </a>
                </div>
                <?php else: ?>
                <div class="app-title">
                    <h2>SecureApp Portal</h2>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <?php if (!$currentUser): ?>
                <!-- Login Form -->
                <section class="login-section">
                    <div class="login-container">
                        <div class="login-header">
                            <i class="fas fa-lock"></i>
                            <h2>Login to SecureApp</h2>
                            <p>Enter your credentials to access the system</p>
                        </div>
                        
                        <?php if ($error): ?>
                            <div class="message-box error">
                                <?php echo htmlspecialchars($error); ?>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" class="login-form">
                            <input type="hidden" name="action" value="login">
                            
                            <div class="form-group">
                                <label for="username">
                                    <i class="fas fa-user"></i>
                                    Username
                                </label>
                                <input type="text" id="username" name="username" class="form-input" placeholder="Enter username" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="password">
                                    <i class="fas fa-key"></i>
                                    Password
                                </label>
                                <input type="password" id="password" name="password" class="form-input" placeholder="Enter password" required>
                            </div>
                            
                            <button type="submit" class="btn btn-login">
                                <i class="fas fa-sign-in-alt"></i>
                                Login
                            </button>
                        </form>
                    </div>
                </section>

                <!-- Challenge Description -->
                <section class="challenge-section">
                    <div class="challenge-card">
                        <div class="challenge-header">
                            <i class="fas fa-flag"></i>
                            <h3>Challenge: JWT None Algorithm Attack</h3>
                        </div>
                        <div class="challenge-content">
                            <p>Your mission is to exploit a JWT vulnerability to gain admin access and capture the flag.</p>
                            
                            <div class="challenge-objectives">
                                <h4>Objective:</h4>
                                <ul>
                                    <li>Find valid credentials to access the system</li>
                                    <li>Analyze the authentication mechanism</li>
                                    <li>Exploit any JWT vulnerabilities</li>
                                    <li>Gain admin privileges</li>
                                    <li>Capture the flag!</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </section>
            <?php else: ?>
                <!-- Dashboard -->
                <section class="dashboard-header">
                    <h2>SecureApp Dashboard</h2>
                    <p>Welcome to your secure application dashboard</p>
                </section>

                <?php if ($currentUser['role'] === 'guest'): ?>
                    <!-- Guest Dashboard -->
                    <section class="dashboard-section">
                        <div class="dashboard-grid">
                            <div class="dashboard-card">
                                <div class="card-icon">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="card-content">
                                    <h3>Profile</h3>
                                    <p>View your basic profile information</p>
                                    <div class="profile-info">
                                        <p><strong>Username:</strong> <?php echo htmlspecialchars($currentUser['username']); ?></p>
                                        <p><strong>Role:</strong> Guest User</p>
                                        <p><strong>Access Level:</strong> Limited</p>
                                    </div>
                                </div>
                            </div>

                            <div class="dashboard-card">
                                <div class="card-icon">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                                <div class="card-content">
                                    <h3>Documents</h3>
                                    <p>Access your available documents</p>
                                    <ul class="document-list">
                                        <li><i class="fas fa-file-pdf"></i> User Manual.pdf</li>
                                        <li><i class="fas fa-file-word"></i> Getting Started.docx</li>
                                        <li><i class="fas fa-file-text"></i> FAQ.txt</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="dashboard-card restricted">
                                <div class="card-icon">
                                    <i class="fas fa-lock"></i>
                                </div>
                                <div class="card-content">
                                    <h3>Admin Panel</h3>
                                    <p>Administrative functions</p>
                                    <div class="restricted-message">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        <p>Access Denied: Admin privileges required</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                <?php else: ?>
                    <!-- Admin Dashboard -->
                    <section class="dashboard-section">
                        <div class="admin-banner">
                            <i class="fas fa-crown"></i>
                            <h3>Administrator Access Granted</h3>
                            <p>You have full administrative privileges</p>
                        </div>

                        <div class="dashboard-grid">
                            <div class="dashboard-card admin-card">
                                <div class="card-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="card-content">
                                    <h3>User Management</h3>
                                    <p>Manage system users and permissions</p>
                                    <div class="admin-stats">
                                        <p>Total Users: <strong>1,247</strong></p>
                                        <p>Active Sessions: <strong>89</strong></p>
                                        <p>Pending Approvals: <strong>12</strong></p>
                                    </div>
                                </div>
                            </div>

                            <div class="dashboard-card flag-card">
                                <div class="card-icon">
                                    <i class="fas fa-flag"></i>
                                </div>
                                <div class="card-content">
                                    <h3>🎉 Congratulations!</h3>
                                    <p>You successfully exploited the JWT None Algorithm vulnerability!</p>
                                    <div class="flag-container">
                                        <h4>Your Flag:</h4>
                                        <div class="flag-box">
                                            <code><?php echo $jwt->getFlag(); ?></code>
                                        </div>
                                    </div>
                                    <div class="achievement">
                                        <i class="fas fa-trophy"></i>
                                        <p><strong>Achievement Unlocked:</strong> JWT Hacker</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <p>&copy; 2024 Cyber Bangla Lab. Developed by <strong>TareqAhamed (0xt4req)</strong></p>
                <div class="footer-links">
                    <a href="https://github.com/0xt4req" class="footer-link">
                        <i class="fab fa-github"></i>
                    </a>
                    <a href="https://twitter.com/0xt4req" class="footer-link">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="https://www.linkedin.com/in/0xt4req/" class="footer-link">
                        <i class="fab fa-linkedin"></i>
                    </a>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>
