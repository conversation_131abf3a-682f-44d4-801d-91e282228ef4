<?php
// JWT Weak Secret Attack Lab - Pure PHP
class JWTLab {
    private $secret = 'secret'; // VULNERABILITY: Very weak secret
    private $users = [
        'guest' => [
            'password' => 'guest',
            'role' => 'guest',
            'id' => 1
        ],
        'admin' => [
            'password' => 'super_secret_admin_password_2024',
            'role' => 'admin',
            'id' => 2
        ]
    ];
    private $flag = 'flag{jwt_weak_secret_cracked}';

    // Base64 URL encode
    private function base64UrlEncode($data) {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }

    // Base64 URL decode
    private function base64UrlDecode($data) {
        return base64_decode(str_pad(strtr($data, '-_', '+/'), strlen($data) % 4, '=', STR_PAD_RIGHT));
    }

    // Create JWT token
    public function createToken($payload) {
        $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
        $payload = json_encode($payload);
        
        $headerEncoded = $this->base64UrlEncode($header);
        $payloadEncoded = $this->base64UrlEncode($payload);
        
        $signature = hash_hmac('sha256', $headerEncoded . '.' . $payloadEncoded, $this->secret, true);
        $signatureEncoded = $this->base64UrlEncode($signature);
        
        return $headerEncoded . '.' . $payloadEncoded . '.' . $signatureEncoded;
    }

    // Verify JWT token - uses weak secret for signing
    public function verifyToken($token) {
        $parts = explode('.', $token);
        
        if (count($parts) !== 3) {
            return false;
        }

        $header = json_decode($this->base64UrlDecode($parts[0]), true);
        $payload = json_decode($this->base64UrlDecode($parts[1]), true);
        
        if (!$header || !$payload) {
            return false;
        }

        // Verify signature for HS256 algorithm
        if ($header['alg'] === 'HS256') {
            $headerEncoded = $parts[0];
            $payloadEncoded = $parts[1];
            $signature = $parts[2];
            
            $expectedSignature = $this->base64UrlEncode(
                hash_hmac('sha256', $headerEncoded . '.' . $payloadEncoded, $this->secret, true)
            );
            
            if ($signature === $expectedSignature) {
                return $payload;
            }
        }

        return false;
    }

    // Authenticate user
    public function authenticate($username, $password) {
        if (isset($this->users[$username]) && $this->users[$username]['password'] === $password) {
            return [
                'id' => $this->users[$username]['id'],
                'username' => $username,
                'role' => $this->users[$username]['role']
            ];
        }
        return false;
    }

    // Get current user from token
    public function getCurrentUser() {
        if (isset($_COOKIE['jwt_token'])) {
            $payload = $this->verifyToken($_COOKIE['jwt_token']);
            if ($payload) {
                return $payload;
            }
        }
        return null;
    }

    // Get flag (only for admin)
    public function getFlag() {
        return $this->flag;
    }
}
?>
