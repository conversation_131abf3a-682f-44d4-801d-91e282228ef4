<?php
session_start();

// Include the JWT Lab class
require_once 'jwt-lab.php';

$jwt = new JWTLab();
$currentUser = $jwt->getCurrentUser();

// Redirect to login if not authenticated
if (!$currentUser) {
    header('Location: index.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - JWT Weak Secret Lab</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-shield-alt"></i>
                    <h1>JWT Weak Secret Lab</h1>
                </div>
                <div class="user-info">
                    <span>Welcome, <strong><?php echo htmlspecialchars($currentUser['username']); ?></strong></span>
                    <span class="user-role"><?php echo htmlspecialchars($currentUser['role']); ?></span>
                    <a href="index.php?action=logout" class="btn btn-logout">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <section class="dashboard-header">
                <h2>Dashboard</h2>
                <p>Welcome to your dashboard</p>
            </section>

            <?php if ($currentUser['role'] === 'guest'): ?>
                <!-- Guest Dashboard -->
                <section class="dashboard-section">
                    <div class="dashboard-grid">
                        <div class="dashboard-card">
                            <div class="card-icon">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="card-content">
                                <h3>Profile</h3>
                                <p>Your basic profile information</p>
                                <div class="profile-info">
                                    <p><strong>Username:</strong> <?php echo htmlspecialchars($currentUser['username']); ?></p>
                                    <p><strong>Role:</strong> Guest User</p>
                                    <p><strong>Access Level:</strong> Limited</p>
                                </div>
                            </div>
                        </div>

                        <div class="dashboard-card restricted">
                            <div class="card-icon">
                                <i class="fas fa-flag"></i>
                            </div>
                            <div class="card-content">
                                <h3>Flag Access</h3>
                                <p>Access to the secret flag</p>
                                <div class="restricted-message">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <p><strong>No flag for guest!</strong><br>Admin access required to view the flag.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            <?php else: ?>
                <!-- Admin Dashboard -->
                <section class="dashboard-section">
                    <div class="admin-banner">
                        <i class="fas fa-crown"></i>
                        <h3>Administrator Access Granted</h3>
                        <p>You have successfully gained admin privileges!</p>
                    </div>

                    <div class="dashboard-grid">
                        <div class="dashboard-card admin-card">
                            <div class="card-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="card-content">
                                <h3>User Management</h3>
                                <p>Manage system users and permissions</p>
                                <div class="admin-stats">
                                    <p>Total Users: <strong>1,247</strong></p>
                                    <p>Active Sessions: <strong>89</strong></p>
                                    <p>Pending Approvals: <strong>12</strong></p>
                                </div>
                            </div>
                        </div>

                        <div class="dashboard-card flag-card">
                            <div class="card-icon">
                                <i class="fas fa-flag"></i>
                            </div>
                            <div class="card-content">
                                <h3>🎉 Congratulations!</h3>
                                <p>You successfully cracked the JWT weak secret!</p>
                                <div class="flag-container">
                                    <h4>Your Flag:</h4>
                                    <div class="flag-box">
                                        <code><?php echo $jwt->getFlag(); ?></code>
                                    </div>
                                </div>
                                <div class="achievement">
                                    <i class="fas fa-trophy"></i>
                                    <p><strong>Achievement Unlocked:</strong> JWT Secret Cracker</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            <?php endif; ?>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <p>&copy; 2024 JWT Weak Secret Lab</p>
            </div>
        </div>
    </footer>
</body>
</html>
