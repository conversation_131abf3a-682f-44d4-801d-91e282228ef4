/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #0c1a2b 0%, #1e3b5d 100%);
    color: #e0e6ed;
    line-height: 1.6;
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: rgba(12, 26, 43, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 2px solid #00c9a7;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 4px 20px rgba(0, 201, 167, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo i {
    font-size: 2rem;
    color: #00c9a7;
    text-shadow: 0 0 10px rgba(0, 201, 167, 0.5);
}

.logo h1 {
    font-size: 1.8rem;
    font-weight: 700;
    color: #00c9a7;
    text-shadow: 0 0 10px rgba(0, 201, 167, 0.3);
}

.app-title h2 {
    font-size: 1.2rem;
    color: #e0e6ed;
    font-weight: 400;
}

/* User Info */
.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-info span {
    color: #e0e6ed;
}

.user-role {
    background: linear-gradient(135deg, #00c9a7, #00a085);
    color: #0c1a2b;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.btn-logout {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.nav {
    display: flex;
    gap: 2rem;
}

.nav-link {
    color: #e0e6ed;
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: #00c9a7;
    background: rgba(0, 201, 167, 0.1);
    transform: translateY(-2px);
}

/* Main Content */
.main {
    flex: 1;
    padding: 2rem 0;
}

/* Login Section */
.login-section {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 70vh;
    padding: 2rem 0;
}

.login-container {
    background: linear-gradient(135deg, rgba(30, 59, 93, 0.3) 0%, rgba(12, 26, 43, 0.5) 100%);
    border: 1px solid rgba(0, 201, 167, 0.2);
    border-radius: 20px;
    padding: 3rem;
    max-width: 450px;
    width: 100%;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(15px);
}

.login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.login-header i {
    font-size: 3rem;
    color: #00c9a7;
    margin-bottom: 1rem;
    text-shadow: 0 0 20px rgba(0, 201, 167, 0.5);
}

.login-header h2 {
    color: #00c9a7;
    margin-bottom: 0.5rem;
    font-size: 1.8rem;
}

.login-header p {
    color: #b8c5d1;
    font-size: 1rem;
}

.login-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.login-form .form-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #00c9a7;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.btn-login {
    background: linear-gradient(135deg, #00c9a7, #00a085);
    color: #0c1a2b;
    padding: 1rem;
    font-size: 1.1rem;
    font-weight: 700;
    margin-top: 1rem;
}

.message-box {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 8px;
    display: block;
}

.message-box.success {
    background: rgba(39, 174, 96, 0.1);
    border: 1px solid #27ae60;
    color: #27ae60;
}

.message-box.error {
    background: rgba(231, 76, 60, 0.1);
    border: 1px solid #e74c3c;
    color: #e74c3c;
}

.message-box.info {
    background: rgba(52, 152, 219, 0.1);
    border: 1px solid #3498db;
    color: #3498db;
}

.login-hint {
    margin-top: 2rem;
    padding: 1rem;
    background: rgba(0, 201, 167, 0.05);
    border: 1px solid rgba(0, 201, 167, 0.2);
    border-radius: 8px;
    text-align: center;
}

.login-hint i {
    color: #00c9a7;
    margin-right: 0.5rem;
}

.login-hint code {
    background: rgba(0, 201, 167, 0.1);
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    color: #00c9a7;
    font-weight: 600;
}

/* Challenge Section */
.challenge-section {
    padding: 2rem 0;
}

.challenge-card {
    background: linear-gradient(135deg, rgba(30, 59, 93, 0.3) 0%, rgba(12, 26, 43, 0.5) 100%);
    border: 1px solid rgba(0, 201, 167, 0.2);
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
}

.challenge-header {
    text-align: center;
    margin-bottom: 2rem;
}

.challenge-header i {
    font-size: 2.5rem;
    color: #00c9a7;
    margin-bottom: 1rem;
}

.challenge-header h3 {
    color: #00c9a7;
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
}

.challenge-objectives {
    background: rgba(0, 201, 167, 0.05);
    border: 1px solid rgba(0, 201, 167, 0.2);
    border-radius: 8px;
    padding: 1.5rem;
    margin: 1.5rem 0;
}

.challenge-objectives h4 {
    color: #00c9a7;
    margin-bottom: 1rem;
}

.challenge-objectives ul {
    margin-left: 1.5rem;
}

.challenge-objectives li {
    margin-bottom: 0.5rem;
    color: #b8c5d1;
}

.challenge-hint {
    background: rgba(243, 156, 18, 0.1);
    border: 1px solid #f39c12;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1.5rem;
    display: flex;
    align-items: center;
    gap: 12px;
}

.challenge-hint i {
    color: #f39c12;
    font-size: 1.2rem;
}

/* Dashboard Styles */
.dashboard-header {
    text-align: center;
    margin-bottom: 3rem;
}

.dashboard-header h2 {
    color: #00c9a7;
    font-size: 2.2rem;
    margin-bottom: 0.5rem;
}

.dashboard-header p {
    color: #b8c5d1;
    font-size: 1.1rem;
}

.dashboard-section {
    margin-bottom: 3rem;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.dashboard-card {
    background: linear-gradient(135deg, rgba(30, 59, 93, 0.3) 0%, rgba(12, 26, 43, 0.5) 100%);
    border: 1px solid rgba(0, 201, 167, 0.2);
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 201, 167, 0.2);
}

.dashboard-card.restricted {
    opacity: 0.6;
    border-color: rgba(231, 76, 60, 0.3);
}

.dashboard-card.admin-card {
    border-color: rgba(0, 201, 167, 0.4);
    background: linear-gradient(135deg, rgba(0, 201, 167, 0.1) 0%, rgba(30, 59, 93, 0.3) 100%);
}

.dashboard-card.flag-card {
    border: 2px solid #00c9a7;
    background: linear-gradient(135deg, rgba(0, 201, 167, 0.2) 0%, rgba(30, 59, 93, 0.4) 100%);
    box-shadow: 0 0 30px rgba(0, 201, 167, 0.3);
}

.card-icon {
    text-align: center;
    margin-bottom: 1rem;
}

.card-icon i {
    font-size: 2.5rem;
    color: #00c9a7;
}

.dashboard-card h3 {
    color: #00c9a7;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.dashboard-card p {
    color: #b8c5d1;
    margin-bottom: 1rem;
}

.profile-info, .admin-stats, .database-info, .server-stats {
    background: rgba(0, 0, 0, 0.2);
    padding: 1rem;
    border-radius: 8px;
    margin-top: 1rem;
}

.profile-info p, .admin-stats p, .database-info p, .server-stats p {
    margin-bottom: 0.5rem;
    color: #e0e6ed;
}

.document-list {
    list-style: none;
    padding: 0;
}

.document-list li {
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(0, 201, 167, 0.1);
    color: #b8c5d1;
}

.document-list li:last-child {
    border-bottom: none;
}

.document-list i {
    color: #00c9a7;
    margin-right: 0.5rem;
}

.stats {
    display: flex;
    gap: 2rem;
    margin-top: 1rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: bold;
    color: #00c9a7;
}

.stat-label {
    font-size: 0.9rem;
    color: #b8c5d1;
}

.restricted-message {
    background: rgba(231, 76, 60, 0.1);
    border: 1px solid #e74c3c;
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
    margin-top: 1rem;
}

.restricted-message i {
    color: #e74c3c;
    margin-right: 0.5rem;
}

.restricted-message p {
    color: #e74c3c;
    margin: 0;
}

.admin-banner {
    background: linear-gradient(135deg, #00c9a7, #00a085);
    color: #0c1a2b;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    margin-bottom: 2rem;
    box-shadow: 0 8px 25px rgba(0, 201, 167, 0.3);
}

.admin-banner i {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.admin-banner h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.flag-container {
    margin-top: 1.5rem;
}

.flag-container h4 {
    color: #00c9a7;
    margin-bottom: 1rem;
}

.flag-box {
    background: rgba(0, 0, 0, 0.4);
    border: 2px solid #00c9a7;
    border-radius: 8px;
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.flag-box code {
    flex: 1;
    color: #00c9a7;
    font-size: 1.1rem;
    font-weight: bold;
    font-family: 'Courier New', monospace;
}

.btn-copy {
    background: linear-gradient(135deg, #00c9a7, #00a085);
    color: #0c1a2b;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.achievement {
    background: rgba(243, 156, 18, 0.1);
    border: 1px solid #f39c12;
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
}

.achievement i {
    color: #f39c12;
    font-size: 1.5rem;
    margin-right: 0.5rem;
}

.achievement p {
    color: #f39c12;
    margin: 0;
    font-weight: 600;
}

/* Section Styles */
.section {
    margin-bottom: 3rem;
}

.section-title {
    font-size: 2rem;
    color: #00c9a7;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 12px;
}

.section-title i {
    font-size: 1.5rem;
}

/* Card Styles */
.card {
    background: linear-gradient(135deg, rgba(30, 59, 93, 0.3) 0%, rgba(12, 26, 43, 0.5) 100%);
    border: 1px solid rgba(0, 201, 167, 0.2);
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
}

.card-content h4 {
    color: #00c9a7;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.card-content p {
    margin-bottom: 1rem;
    color: #b8c5d1;
}

.card-content ul, .card-content ol {
    margin-left: 1.5rem;
    margin-bottom: 1rem;
}

.card-content li {
    margin-bottom: 0.5rem;
    color: #b8c5d1;
}

/* Lab Steps */
.lab-step {
    margin-bottom: 2rem;
}

.step-title {
    font-size: 1.5rem;
    color: #00c9a7;
    margin-bottom: 1rem;
    padding-left: 1rem;
    border-left: 4px solid #00c9a7;
}

/* Form Styles */
.form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    color: #00c9a7;
    font-weight: 600;
}

.form-input, .form-textarea {
    background: rgba(12, 26, 43, 0.7);
    border: 2px solid rgba(0, 201, 167, 0.3);
    border-radius: 8px;
    padding: 0.75rem;
    color: #e0e6ed;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-input:focus, .form-textarea:focus {
    outline: none;
    border-color: #00c9a7;
    box-shadow: 0 0 10px rgba(0, 201, 167, 0.3);
}

.form-textarea {
    resize: vertical;
    font-family: 'Courier New', monospace;
}

/* Button Styles */
.btn {
    background: linear-gradient(135deg, #00c9a7, #00a085);
    color: #0c1a2b;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    justify-content: center;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 201, 167, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #1e3b5d, #2a4a6b);
    color: #00c9a7;
    border: 2px solid #00c9a7;
}

.btn-danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
}

/* Result Box */
.result-box {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid #00c9a7;
    background: rgba(0, 201, 167, 0.1);
    display: none;
}

.result-box.success {
    border-left-color: #27ae60;
    background: rgba(39, 174, 96, 0.1);
}

.result-box.error {
    border-left-color: #e74c3c;
    background: rgba(231, 76, 60, 0.1);
}

.result-box.warning {
    border-left-color: #f39c12;
    background: rgba(243, 156, 18, 0.1);
}

/* Token Section */
.token-section {
    margin-top: 3rem;
}

.token-card {
    background: linear-gradient(135deg, rgba(30, 59, 93, 0.3) 0%, rgba(12, 26, 43, 0.5) 100%);
    border: 1px solid rgba(0, 201, 167, 0.2);
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
}

.token-card h3 {
    color: #00c9a7;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.token-display {
    margin-bottom: 1rem;
}

.token-textarea {
    width: 100%;
    min-height: 120px;
    background: rgba(12, 26, 43, 0.7);
    border: 2px solid rgba(0, 201, 167, 0.3);
    border-radius: 8px;
    padding: 1rem;
    color: #e0e6ed;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    resize: vertical;
    margin-bottom: 1rem;
}

.token-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Token Analysis */
.token-analysis {
    background: rgba(12, 26, 43, 0.7);
    border: 1px solid rgba(0, 201, 167, 0.3);
    border-radius: 8px;
    padding: 1rem;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    margin-top: 1rem;
}

.token-part {
    margin-bottom: 1rem;
    padding: 0.5rem;
    border-radius: 4px;
}

.token-header {
    background: rgba(52, 152, 219, 0.2);
    border-left: 4px solid #3498db;
}

.token-payload {
    background: rgba(46, 204, 113, 0.2);
    border-left: 4px solid #2ecc71;
}

.token-signature {
    background: rgba(231, 76, 60, 0.2);
    border-left: 4px solid #e74c3c;
}

/* Warning Box */
.warning {
    background: rgba(243, 156, 18, 0.1);
    border: 1px solid #f39c12;
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.warning i {
    color: #f39c12;
    font-size: 1.2rem;
}

/* Objectives */
.objectives {
    background: rgba(0, 201, 167, 0.05);
    border: 1px solid rgba(0, 201, 167, 0.2);
    border-radius: 8px;
    padding: 1.5rem;
    margin: 1rem 0;
}

.objectives h4 {
    color: #00c9a7;
    margin-bottom: 1rem;
}

/* Footer */
.footer {
    background: rgba(12, 26, 43, 0.95);
    border-top: 2px solid #00c9a7;
    padding: 2rem 0;
    margin-top: 4rem;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-content p {
    color: #b8c5d1;
}

.footer-content strong {
    color: #00c9a7;
}

.footer-links {
    display: flex;
    gap: 1rem;
}

.footer-link {
    color: #b8c5d1;
    font-size: 1.5rem;
    transition: all 0.3s ease;
}

.footer-link:hover {
    color: #00c9a7;
    transform: translateY(-2px);
}

/* Code Example */
.code-example {
    background: rgba(12, 26, 43, 0.8);
    border: 1px solid rgba(0, 201, 167, 0.3);
    border-radius: 8px;
    padding: 1.5rem;
    margin: 1.5rem 0;
}

.code-example h4 {
    color: #00c9a7;
    margin-bottom: 1rem;
}

.code-example pre {
    background: rgba(0, 0, 0, 0.4);
    padding: 1rem;
    border-radius: 4px;
    overflow-x: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.4;
}

.code-example code {
    color: #e0e6ed;
}

/* Resources Grid */
.resources-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

.resource-item {
    background: rgba(0, 201, 167, 0.05);
    border: 1px solid rgba(0, 201, 167, 0.2);
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
}

.resource-item:hover {
    background: rgba(0, 201, 167, 0.1);
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 201, 167, 0.2);
}

.resource-item i {
    font-size: 2rem;
    color: #00c9a7;
    margin-bottom: 1rem;
}

.resource-item h5 {
    color: #00c9a7;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.resource-item p {
    color: #b8c5d1;
    font-size: 0.9rem;
}

/* Enhanced animations */
@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(0, 201, 167, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(0, 201, 167, 0); }
    100% { box-shadow: 0 0 0 0 rgba(0, 201, 167, 0); }
}

.btn:focus {
    animation: pulse 1.5s infinite;
}

/* Loading animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(0, 201, 167, 0.3);
    border-radius: 50%;
    border-top-color: #00c9a7;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .nav {
        gap: 1rem;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
    }

    .resources-grid {
        grid-template-columns: 1fr;
    }

    .hero-badges {
        flex-direction: column;
        align-items: center;
    }
}
